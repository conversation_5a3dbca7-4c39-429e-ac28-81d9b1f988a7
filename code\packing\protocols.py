from dataclasses import dataclass
from typing import Generator, Iterable, Optional, Protocol, Tuple

import math


# ---- Domain protocols ----


class PlacementHeuristicProtocol(Protocol):
    """Proposes candidate drop poses for a given box and crate state.

    Implementations should be deterministic for the same inputs to make comparisons reproducible.
    """

    def propose(
        self, crate: "Crate", box: "Box3D", placed: Iterable["PlacedBox"], max_candidates: int
    ) -> Iterable["PlacedBox"]: ...


class ScoringHeuristicProtocol(Protocol):
    """Scores a placed box candidate. Higher is better."""

    def score(
        self,
        crate: "Crate",
        simulator: "PhysicsSimulatorProtocol",
        candidate: "PlacedBox",
        placed: Iterable["PlacedBox"],
    ) -> float: ...


class PhysicsSimulatorProtocol(Protocol):
    """Abstract physics simulator used to evaluate and finalize placements."""

    def simulate_drop(
        self, crate: "Crate", candidate: "PlacedBox", max_steps: int = 150
    ) -> Optional["PlacedBox"]:
        """Simulate a vertical drop from above. Returns final settled pose or None if invalid."""
        ...

    def vertical_clearance_ok(self, crate: "Crate", candidate: "PlacedBox") -> bool:
        """Check that the box can travel vertically down to its final Z without collisions.
        Implement a conservative, fast check (e.g., AABB column test)."""
        ...

    def place_static(self, crate: "Crate", placed: "PlacedBox") -> None:
        """Commit a settled box as static to the world (frozen)."""
        ...

    def world_reset(self, crate: "Crate", placed: Iterable["PlacedBox"]) -> None:
        """Rebuild the static world from scratch for determinism (optional)."""
        ...


class PackerInputProtocol(Protocol):
    """Provides a stream of incoming boxes for packing."""

    def stream(self) -> Generator["Box3D", None, None]: ...


class PackerVisualizerProtocol(Protocol):
    """Optional visual feedback for packing."""

    def update(self, crate: "Crate", placed: Iterable["PlacedBox"]) -> None: ...


class ConstraintProtocol(Protocol):
    """Constraint that must hold for a candidate placement.

    Implementations can check bounds, vertical path collisions, etc.
    Return True if the candidate is acceptable, False otherwise.
    """

    def check(
        self,
        crate: "Crate",
        candidate: "PlacedBox",
        placed: Iterable["PlacedBox"],
        simulator: Optional["PhysicsSimulatorProtocol"] = None,
    ) -> bool: ...


# ---- Core dataclasses kept minimal to avoid import cycles ----


@dataclass
class Box3D:
    width: float
    depth: float
    height: float
    id: Optional[int] = None


@dataclass(init=False)
class Pose:
    x: float
    y: float
    z: float
    qx: float
    qy: float
    qz: float
    qw: float

    def __init__(
        self,
        x: float,
        y: float,
        z: float,
        yaw: float | None = None,
        quat: tuple[float, float, float, float] | None = None,
    ) -> None:
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
        if quat is not None:
            self.qx, self.qy, self.qz, self.qw = (
                float(quat[0]),
                float(quat[1]),
                float(quat[2]),
                float(quat[3]),
            )
        else:
            # Default to yaw-only rotation if not provided
            yaw = float(yaw or 0.0)
            cz = math.cos(yaw * 0.5)
            sz = math.sin(yaw * 0.5)
            # Yaw-only quaternion around Z: (x=0, y=0, z=sin(yaw/2), w=cos(yaw/2))
            self.qx, self.qy, self.qz, self.qw = 0.0, 0.0, sz, cz

    @property
    def quat(self) -> tuple[float, float, float, float]:
        return (self.qx, self.qy, self.qz, self.qw)

    @property
    def yaw(self) -> float:
        """Return yaw (rotation around Z) extracted from quaternion, in radians."""
        x, y, z, w = self.qx, self.qy, self.qz, self.qw
        # yaw (z-axis rotation)
        siny_cosp = 2.0 * (w * z + x * y)
        cosy_cosp = 1.0 - 2.0 * (y * y + z * z)
        return math.atan2(siny_cosp, cosy_cosp)

    def as_euler(self, order: str = "zxy") -> tuple[float, float, float]:
        """Compute Euler angles from the quaternion.

        Default order is 'zxy' to match usages that want yaw-like first angle.
        Returns a tuple of three angles in radians.
        """
        x, y, z, w = self.qx, self.qy, self.qz, self.qw
        # Rotation matrix from quaternion
        xx, yy, zz = x * x, y * y, z * z
        xy, xz, yz = x * y, x * z, y * z
        wx, wy, wz = w * x, w * y, w * z
        r00 = 1.0 - 2.0 * (yy + zz)
        r01 = 2.0 * (xy - wz)
        r02 = 2.0 * (xz + wy)
        r10 = 2.0 * (xy + wz)
        r11 = 1.0 - 2.0 * (xx + zz)
        r12 = 2.0 * (yz - wx)
        r20 = 2.0 * (xz - wy)
        r21 = 2.0 * (yz + wx)
        r22 = 1.0 - 2.0 * (xx + yy)
        if order.lower() == "zxy":
            # ZXY convention
            # z = atan2(r10, r00)
            z_ang = math.atan2(r10, r00)
            # x = asin(-r20)
            x_ang = math.asin(max(-1.0, min(1.0, -r20)))
            # y = atan2(r21, r22)
            y_ang = math.atan2(r21, r22)
            return (z_ang, x_ang, y_ang)
        elif order.lower() == "zyx":
            # ZYX (yaw, pitch, roll)
            yaw = math.atan2(r10, r00)
            pitch = math.asin(max(-1.0, min(1.0, -r20)))
            roll = math.atan2(r21, r22)
            return (yaw, pitch, roll)
        else:
            # Default provide yaw, pitch, roll (ZYX)
            yaw = math.atan2(r10, r00)
            pitch = math.asin(max(-1.0, min(1.0, -r20)))
            roll = math.atan2(r21, r22)
            return (yaw, pitch, roll)


@dataclass
class PlacedBox:
    box: Box3D
    pose: Pose


@dataclass
class Crate:
    width: float
    depth: float
    max_height: float
